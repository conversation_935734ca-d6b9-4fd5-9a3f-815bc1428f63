// Copyright (c) 2025, <PERSON><PERSON> and contributors
// For license information, please see license.txt

frappe.ui.form.on("Rmc", {
    refresh: function(frm) {
        frm.events.calculate_total_qty(frm);
    },

    onload: function(frm) {
        frm.events.calculate_total_qty(frm);
    },

    // Event handler for when the child table is rendered
    table_gaue_on_form_rendered: function(frm) {
        frm.events.calculate_total_qty(frm);
    },

    // Parent form events for table operations
    table_gaue_add: function(frm, cdt, cdn) {
        frm.events.calculate_total_qty(frm);
    },

    table_gaue_remove: function(frm, cdt, cdn) {
        frm.events.calculate_total_qty(frm);
    },

    calculate_total_qty: function(frm) {
        let total_qty = 0;
        console.log("Calculating total qty...");
        console.log("table_gaue:", frm.doc.table_gaue);

        if (frm.doc.table_gaue && frm.doc.table_gaue.length > 0) {
            frm.doc.table_gaue.forEach(function(row) {
                console.log("Row qty:", row.qty);
                if (row.qty) {
                    total_qty += flt(row.qty);
                }
            });
        }

        console.log("Total calculated:", total_qty);
        frm.set_value('total_qty', total_qty);
        frm.refresh_field('total_qty');
    }
});

// Child table events for Rmc table
frappe.ui.form.on("Rmc table", {
    qty: function(frm, cdt, cdn) {
        frm.events.calculate_total_qty(frm);
    }
});
